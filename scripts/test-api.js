#!/usr/bin/env node

/**
 * Simple script to test API endpoints
 * Usage: node scripts/test-api.js [base-url]
 * Example: node scripts/test-api.js https://your-app.vercel.app
 */

const baseUrl = process.argv[2] || 'http://localhost:5000';

async function testEndpoint(endpoint, method = 'GET', body = null) {
  const url = `${baseUrl}${endpoint}`;
  console.log(`\n🧪 Testing ${method} ${url}`);
  
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(url, options);
    const data = await response.text();
    
    console.log(`✅ Status: ${response.status} ${response.statusText}`);
    
    try {
      const jsonData = JSON.parse(data);
      console.log('📄 Response:', JSON.stringify(jsonData, null, 2));
    } catch {
      console.log('📄 Response:', data);
    }
    
    return { status: response.status, data };
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { error: error.message };
  }
}

async function runTests() {
  console.log(`🚀 Testing API endpoints at: ${baseUrl}`);
  
  // Test health endpoint
  await testEndpoint('/api/health');
  
  // Test manifest.json
  await testEndpoint('/manifest.json');
  
  // Test contact form with valid data
  await testEndpoint('/api/contact', 'POST', {
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    message: 'This is a test message'
  });
  
  // Test contact form with invalid data
  await testEndpoint('/api/contact', 'POST', {
    firstName: 'Test',
    email: 'invalid-email',
    message: 'This should fail'
  });
  
  // Test newsletter signup
  await testEndpoint('/api/newsletter', 'POST', {
    email: '<EMAIL>'
  });

  // Test prospect signup
  await testEndpoint('/api/prospects/signup', 'POST', {
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'Prospect',
    gdprConsent: true,
    newsletterSubscribed: true
  });

  // Test prospect signup with invalid data
  await testEndpoint('/api/prospects/signup', 'POST', {
    email: 'invalid-email',
    gdprConsent: false
  });

  console.log('\n✨ Tests completed!');
}

runTests().catch(console.error);
