#!/usr/bin/env node

/**
 * Codemod to replace PNG imports with WebP equivalents
 * Usage: jscodeshift -t scripts/png-to-webp-codemod.js client/src
 */

export default function transformer(fileInfo, api) {
  const j = api.jscodeshift;
  const root = j(fileInfo.source);

  // Track if any changes were made
  let hasChanges = false;

  // Transform import declarations
  root.find(j.ImportDeclaration).forEach(path => {
    const source = path.value.source;
    if (source && source.type === 'Literal' && typeof source.value === 'string') {
      const importPath = source.value;
      
      // Handle imports from client/src/assets (relative imports)
      if (importPath.includes('../assets/') && importPath.endsWith('.png')) {
        const newPath = importPath.replace('../assets/', '@/assets/webp/').replace('.png', '.webp');
        source.value = newPath;
        hasChanges = true;
        console.log(`Updated import: ${importPath} -> ${newPath}`);
      }
      
      // Handle imports from @assets alias (attached_assets)
      if (importPath.startsWith('@assets/') && importPath.endsWith('.png')) {
        const newPath = importPath.replace('@assets/', '@/assets/webp/').replace('.png', '.webp');
        source.value = newPath;
        hasChanges = true;
        console.log(`Updated import: ${importPath} -> ${newPath}`);
      }
    }
  });

  // Transform require calls (if any)
  root.find(j.CallExpression, {
    callee: { name: 'require' }
  }).forEach(path => {
    const arg = path.value.arguments[0];
    if (arg && arg.type === 'Literal' && typeof arg.value === 'string') {
      const requirePath = arg.value;
      
      // Handle require from client/src/assets
      if (requirePath.includes('../assets/') && requirePath.endsWith('.png')) {
        const newPath = requirePath.replace('../assets/', '@/assets/webp/').replace('.png', '.webp');
        arg.value = newPath;
        hasChanges = true;
        console.log(`Updated require: ${requirePath} -> ${newPath}`);
      }
      
      // Handle require from @assets alias
      if (requirePath.startsWith('@assets/') && requirePath.endsWith('.png')) {
        const newPath = requirePath.replace('@assets/', '@/assets/webp/').replace('.png', '.webp');
        arg.value = newPath;
        hasChanges = true;
        console.log(`Updated require: ${requirePath} -> ${newPath}`);
      }
    }
  });

  return hasChanges ? root.toSource() : null;
}
