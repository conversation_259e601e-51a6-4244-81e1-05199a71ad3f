#!/usr/bin/env node

import sharp from 'sharp';
import { readdir, mkdir } from 'fs/promises';
import { join, basename, extname } from 'path';
import { existsSync } from 'fs';

async function convertImages() {
  // Ensure output directory exists
  if (!existsSync('assets/webp')) {
    await mkdir('assets/webp', { recursive: true });
  }

  // Convert images from attached_assets
  console.log('Converting images from attached_assets...');
  const attachedAssets = await readdir('attached_assets');
  const pngFiles = attachedAssets.filter(file => extname(file).toLowerCase() === '.png');
  
  for (const file of pngFiles) {
    const inputPath = join('attached_assets', file);
    const outputName = basename(file, '.png') + '.webp';
    const outputPath = join('assets/webp', outputName);
    
    try {
      await sharp(inputPath)
        .webp({ quality: 80 })
        .toFile(outputPath);
      
      console.log(`✓ Converted ${file} -> ${outputName}`);
    } catch (error) {
      console.error(`✗ Failed to convert ${file}:`, error.message);
    }
  }

  // Convert images from client/src/assets
  console.log('\nConverting images from client/src/assets...');
  const clientAssets = await readdir('client/src/assets');
  const clientPngFiles = clientAssets.filter(file => extname(file).toLowerCase() === '.png');
  
  for (const file of clientPngFiles) {
    const inputPath = join('client/src/assets', file);
    const outputName = basename(file, '.png') + '.webp';
    const outputPath = join('assets/webp', outputName);
    
    try {
      await sharp(inputPath)
        .webp({ quality: 80 })
        .toFile(outputPath);
      
      console.log(`✓ Converted ${file} -> ${outputName}`);
    } catch (error) {
      console.error(`✗ Failed to convert ${file}:`, error.message);
    }
  }

  console.log('\n✅ Image conversion complete!');
}

convertImages().catch(console.error);
