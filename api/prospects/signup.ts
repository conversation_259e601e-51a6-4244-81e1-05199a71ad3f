import { VercelRequest, VercelResponse } from "@vercel/node";
import { createClient } from "@supabase/supabase-js";
import { Resend } from "resend";
import { z } from "zod";
import { nanoid } from "nanoid";

// Validation schema for prospect signup (EXACT as specified)
const prospectSignupSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  phone: z.string().optional(),

  // Signup context - EXACT enum values
  signupSource: z
    .enum([
      "website",
      "landing_page",
      "referral",
      "social_media",
      "advertisement",
      "other",
    ])
    .default("website"),
  signupPage: z.string().optional(),
  utmSource: z.string().optional(),
  utmMedium: z.string().optional(),
  utmCampaign: z.string().optional(),
  utmContent: z.string().optional(),
  utmTerm: z.string().optional(),

  // Legal interests - EXACT enum values
  practiceAreaInterest: z
    .array(z.enum(["personal_injury", "criminal_defense", "family_law"]))
    .default([]),
  caseUrgency: z
    .enum(["immediate", "within_month", "within_quarter", "planning_ahead"])
    .optional(),
  estimatedCaseValue: z
    .enum(["under_10k", "10k_50k", "50k_100k", "over_100k", "unknown"])
    .optional(),

  // Preferences
  newsletterSubscribed: z.boolean().default(true),
  marketingConsent: z.boolean().default(false),
  communicationPreferences: z
    .object({
      email: z.boolean().default(true),
      sms: z.boolean().default(false),
      phone: z.boolean().default(false),
    })
    .default({ email: true, sms: false, phone: false }),

  // GDPR - REQUIRED
  gdprConsent: z.boolean().refine((val: boolean) => val === true, {
    message: "GDPR consent is required",
  }),

  // Bot protection
  turnstileToken: z.string().optional(),
});

// Helper function to get client IP
function getClientIP(req: VercelRequest): string {
  const forwarded = req.headers["x-forwarded-for"];
  const realIP = req.headers["x-real-ip"];

  if (forwarded) {
    return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(",")[0];
  }
  if (realIP) {
    return Array.isArray(realIP) ? realIP[0] : realIP;
  }
  return "unknown";
}

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Enable CORS
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method Not Allowed" });
  }

  // Check environment variables
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey =
    process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_KEY;
  const resendApiKey = process.env.RESEND_API_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error("Missing Supabase configuration");
    return res.status(500).json({
      error: "Server configuration error",
      details: "Missing database configuration",
    });
  }

  try {
    // Validate request body
    const validatedData = prospectSignupSchema.parse(req.body);

    // Initialize Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get client information for attribution
    const ip = getClientIP(req);
    const userAgent = req.headers["user-agent"] || "unknown";
    const referrer = req.headers["referer"] || req.headers["referrer"] || null;

    // Generate email verification token
    const emailVerificationToken = nanoid(32);

    // Prepare data for database (EXACT field mapping as specified)
    const prospectData = {
      email: validatedData.email,
      first_name: validatedData.firstName,
      last_name: validatedData.lastName,
      phone: validatedData.phone,
      signup_source: validatedData.signupSource,
      signup_page: validatedData.signupPage,
      utm_source: validatedData.utmSource,
      utm_medium: validatedData.utmMedium,
      utm_campaign: validatedData.utmCampaign,
      utm_content: validatedData.utmContent,
      utm_term: validatedData.utmTerm,
      practice_area_interest: validatedData.practiceAreaInterest,
      case_urgency: validatedData.caseUrgency,
      estimated_case_value: validatedData.estimatedCaseValue,
      newsletter_subscribed: validatedData.newsletterSubscribed,
      marketing_consent: validatedData.marketingConsent,
      communication_preferences: validatedData.communicationPreferences,
      gdpr_consent: validatedData.gdprConsent,
      gdpr_consent_date: new Date().toISOString(),
      gdpr_consent_ip: ip,
      gdpr_consent_user_agent: userAgent,
      data_retention_until: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
      ip_address: ip,
      user_agent: userAgent,
      referrer_url: referrer,
      timezone: "UTC",
      locale: "en-US",
      email_verification_token: emailVerificationToken,
      email_verified: false,
      status: "active",
    };

    // Insert prospect into database
    const { data: insertedProspect, error: insertError } = await supabase
      .from("prospects")
      .insert([prospectData])
      .select("id, email, first_name, last_name")
      .single();

    if (insertError) {
      console.error("Database insert error:", insertError);

      // Handle duplicate email
      if (insertError.code === "23505") {
        return res.status(409).json({
          error: "Email already registered",
          message:
            "This email address is already in our system. Please check your inbox for verification email.",
        });
      }

      return res.status(500).json({
        error: "Database error",
        message: "Failed to save your information. Please try again.",
      });
    }

    // Send verification email if Resend is configured
    if (resendApiKey && insertedProspect) {
      try {
        const resend = new Resend(resendApiKey);
        const verificationUrl = `${req.headers.origin || "https://www.ailexlaw.com"}/verify-email?token=${emailVerificationToken}`;

        await resend.emails.send({
          from: "AiLex Legal Assistant <<EMAIL>>",
          to: [validatedData.email],
          subject: "Please verify your email address",
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #1EAEDB;">Welcome to AiLex!</h2>
              <p>Hi ${validatedData.firstName || "there"},</p>
              <p>Thank you for your interest in AiLex Legal Assistant. Please verify your email address to complete your signup:</p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${verificationUrl}" style="background-color: #1EAEDB; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Verify Email Address</a>
              </div>
              <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
              <p>This link will expire in 24 hours for security reasons.</p>
              <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
              <p style="color: #666; font-size: 12px;">
                You're receiving this email because you signed up for updates from AiLex Legal Assistant.
                If you didn't request this, please ignore this email.
              </p>
            </div>
          `,
        });
      } catch (emailError) {
        console.error("Email sending error:", emailError);
        // Don't fail the signup if email fails
      }
    }

    // Log interaction
    if (insertedProspect) {
      await supabase.from("prospect_interactions").insert([
        {
          prospect_id: insertedProspect.id,
          interaction_type: "signup",
          subject: "Prospect signup",
          metadata: {
            signup_source: validatedData.signupSource,
            signup_page: validatedData.signupPage,
            utm_params: {
              source: validatedData.utmSource,
              medium: validatedData.utmMedium,
              campaign: validatedData.utmCampaign,
              content: validatedData.utmContent,
              term: validatedData.utmTerm,
            },
            user_agent: userAgent,
            ip_address: ip,
          },
        },
      ]);
    }

    // Return success response
    res.status(200).json({
      success: true,
      message:
        "Successfully signed up! Please check your email to verify your address.",
      prospectId: insertedProspect?.id,
      emailVerified: false,
    });
  } catch (error) {
    console.error("Prospect signup API error:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Invalid form data",
        details: error.format(),
        message: "Please check your form data and try again.",
      });
    }

    res.status(500).json({
      error: "Internal server error",
      message: "An unexpected error occurred. Please try again.",
    });
  }
}
