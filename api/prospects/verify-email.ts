import { VercelRequest, VercelResponse } from "@vercel/node";
import { createClient } from "@supabase/supabase-js";
import { z } from "zod";

// Validation schemas
const verifyEmailSchema = z.object({
  token: z.string().min(1, "Verification token is required"),
});

const checkEmailSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Enable CORS
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "PUT, GET, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  // Check environment variables
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey =
    process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error("Missing Supabase configuration");
    return res.status(500).json({
      error: "Server configuration error",
      details: "Missing database configuration",
    });
  }

  // Initialize Supabase client
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    if (req.method === "PUT") {
      // Verify email with token
      const { token } = verifyEmailSchema.parse(req.body);

      // Find prospect with this verification token
      const { data: prospect, error: findError } = await supabase
        .from("prospects")
        .select("id, email, email_verified, first_name")
        .eq("email_verification_token", token)
        .single();

      if (findError || !prospect) {
        return res.status(400).json({
          success: false,
          error: "Invalid verification token",
          message:
            "The verification link is invalid or has expired. Please request a new verification email.",
        });
      }

      if (prospect.email_verified) {
        return res.status(200).json({
          success: true,
          message: "Email address is already verified.",
        });
      }

      // Update prospect to mark email as verified
      const { error: updateError } = await supabase
        .from("prospects")
        .update({
          email_verified: true,
          email_verification_token: null, // Clear the token
          updated_at: new Date().toISOString(),
        })
        .eq("id", prospect.id);

      if (updateError) {
        console.error("Database update error:", updateError);
        return res.status(500).json({
          success: false,
          error: "Verification failed",
          message: "Unable to verify email. Please try again.",
        });
      }

      // Log interaction
      await supabase.from("prospect_interactions").insert([
        {
          prospect_id: prospect.id,
          interaction_type: "email_verified",
          subject: "Email verification completed",
          metadata: {
            verified_at: new Date().toISOString(),
          },
        },
      ]);

      return res.status(200).json({
        success: true,
        message: `Email verified successfully! Welcome to AiLex, ${prospect.first_name || "there"}.`,
      });
    } else if (req.method === "GET") {
      // Check email verification status
      const email = req.query.email as string;

      if (!email) {
        return res.status(400).json({
          error: "Email parameter is required",
        });
      }

      const { email: validatedEmail } = checkEmailSchema.parse({ email });

      // Find prospect by email
      const { data: prospect, error: findError } = await supabase
        .from("prospects")
        .select("email_verified")
        .eq("email", validatedEmail)
        .single();

      if (findError || !prospect) {
        return res.status(404).json({
          error: "Email not found",
          message: "No prospect found with this email address",
        });
      }

      return res.status(200).json({
        emailVerified: prospect.email_verified || false,
      });
    } else {
      return res.status(405).json({ error: "Method Not Allowed" });
    }
  } catch (error) {
    console.error("Email verification API error:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Invalid request data",
        details: error.format(),
      });
    }

    res.status(500).json({
      error: "Internal server error",
      message: "An unexpected error occurred. Please try again.",
    });
  }
}
