import { VercelRequest, VercelResponse } from "@vercel/node";
import { createClient } from "@supabase/supabase-js";
import { Resend } from "resend";
import { z } from "zod";

// Validation schemas
const contactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  message: z.string().min(1, "Message is required"),
});

const contactSchemaWithNames = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  message: z.string().min(1, "Message is required"),
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Enable CORS
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method Not Allowed" });
  }

  // Check environment variables early
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const resendApiKey = process.env.RESEND_API_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error("Missing Supabase configuration:", {
      hasUrl: !!supabaseUrl,
      hasKey: !!supabaseKey,
    });
    return res.status(500).json({
      error: "Server configuration error",
      details: "Missing database configuration",
    });
  }

  try {
    // Validate request body - handle both single name and first/last name formats
    let name: string;
    let email: string;
    let message: string;

    // Try to parse as first/last name format first
    try {
      const {
        firstName,
        lastName,
        email: emailField,
        message: messageField,
      } = contactSchemaWithNames.parse(req.body);
      name = `${firstName} ${lastName}`;
      email = emailField;
      message = messageField;
    } catch {
      // Fall back to single name format
      const parsed = contactSchema.parse(req.body);
      name = parsed.name;
      email = parsed.email;
      message = parsed.message;
    }

    // Initialize Supabase client (already validated above)
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Store in Supabase
    const { error: dbError } = await supabase
      .from("contact_submissions")
      .insert({ name, email, message });

    if (dbError) {
      console.error("Database error:", dbError);
      return res.status(500).json({
        error: "Failed to save submission",
        details:
          process.env.NODE_ENV === "development" ? dbError.message : undefined,
      });
    }

    // Send confirmation email
    if (resendApiKey) {
      try {
        const resend = new Resend(resendApiKey);
        await resend.emails.send({
          from: "AiLex <<EMAIL>>",
          to: email,
          subject: "We received your message - AiLex",
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #1EAEDB;">Thanks for reaching out!</h2>
              <p>Hi ${name},</p>
              <p>We received your message and will get back to you within 24 hours.</p>
              <p>Your message:</p>
              <blockquote style="border-left: 3px solid #1EAEDB; padding-left: 15px; margin: 20px 0; color: #666;">
                ${message}
              </blockquote>
              <p>Best regards,<br>The AiLex Team</p>
            </div>
          `,
        });
      } catch (emailError) {
        console.error("Email error:", emailError);
        // Don't fail the request if email fails
      }
    }

    res.status(200).json({
      success: true,
      message: "Contact submission received successfully",
    });
  } catch (error) {
    console.error("Contact API error:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Invalid form data",
        details: error.format(),
      });
    }

    res.status(500).json({ error: "Internal server error" });
  }
}
