import { VercelRequest, VercelResponse } from "@vercel/node";
import { createClient } from "@supabase/supabase-js";

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Enable CORS
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method Not Allowed" });
  }

  try {
    // Check environment variables
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey =
      process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_KEY;
    const resendApiKey = process.env.RESEND_API_KEY;

    console.log("Environment check:", {
      hasSupabaseUrl: !!supabaseUrl,
      hasSupabaseKey: !!supabaseServiceKey,
      hasResendKey: !!resendApiKey,
      supabaseUrlPreview: supabaseUrl
        ? `${supabaseUrl.substring(0, 20)}...`
        : "missing",
    });

    if (!supabaseUrl || !supabaseServiceKey) {
      return res.status(500).json({
        error: "Missing Supabase configuration",
        details: {
          hasSupabaseUrl: !!supabaseUrl,
          hasSupabaseKey: !!supabaseServiceKey,
          hasResendKey: !!resendApiKey,
        },
      });
    }

    // Initialize Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Test connection by checking if prospects table exists
    const { data, error, count } = await supabase
      .from("prospects")
      .select("id", { count: "exact", head: true });

    if (error) {
      console.error("Supabase connection error:", error);
      return res.status(500).json({
        error: "Database connection failed",
        details: error.message,
        hint: "Check if the prospects table exists and your service key has the right permissions",
      });
    }

    // Test if prospect_interactions table exists
    const { error: interactionsError } = await supabase
      .from("prospect_interactions")
      .select("id", { count: "exact", head: true });

    const response = {
      success: true,
      message: "Supabase connection successful!",
      database: {
        connected: true,
        prospectsTable: !error,
        prospectsCount: count || 0,
        interactionsTable: !interactionsError,
      },
      environment: {
        hasSupabaseUrl: !!supabaseUrl,
        hasSupabaseKey: !!supabaseServiceKey,
        hasResendKey: !!resendApiKey,
        supabaseUrlPreview: supabaseUrl
          ? `${supabaseUrl.substring(0, 30)}...`
          : "missing",
      },
    };

    if (interactionsError) {
      response.database.interactionsTable = false;
      console.warn("prospect_interactions table issue:", interactionsError);
    }

    res.status(200).json(response);
  } catch (error) {
    console.error("Test endpoint error:", error);
    res.status(500).json({
      error: "Test failed",
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    });
  }
}
