# Troubleshooting Guide

## Common Issues and Solutions

### 1. 401 Unauthorized <PERSON>rror (manifest.json)

**Problem**: <PERSON><PERSON><PERSON> shows "Failed to load resource: the server responded with a status of 401" for manifest.json

**Causes**:
- Vercel deployment has authentication middleware
- Manifest.json is being served from a protected route
- Incorrect rewrite rules in vercel.json

**Solutions**:
1. Check your vercel.json configuration - ensure static files are not being rewritten
2. Verify that manifest.json is in the correct location (`client/public/manifest.json`)
3. Make sure your Vercel deployment doesn't have basic auth enabled for static files

### 2. 500 Internal Server Error (API Contact)

**Problem**: Contact form submission fails with 500 error

**Causes**:
- Missing environment variables
- Database connection issues
- Invalid Supabase configuration

**Solutions**:
1. **Check Environment Variables**:
   ```bash
   # Test your API health
   npm run test:api https://your-app.vercel.app
   ```

2. **Verify Supabase Setup**:
   - Ensure `SUPABASE_URL` is correct
   - Verify `SUPABASE_SERVICE_ROLE_KEY` has proper permissions
   - Check that the `contact_submissions` table exists

3. **Check Vercel Environment Variables**:
   - Go to Vercel Dashboard → Your Project → Settings → Environment Variables
   - Ensure all required variables are set for Production, Preview, and Development

### 3. CORS Issues

**Problem**: Cross-origin request blocked

**Solutions**:
- API endpoints already include CORS headers
- If still having issues, check if your domain is correctly configured

### 4. Email Sending Issues

**Problem**: Emails not being sent (but form submission works)

**Causes**:
- Missing or invalid `RESEND_API_KEY`
- Incorrect email configuration

**Solutions**:
1. Verify your Resend API key is valid
2. Check the email domain is verified in Resend
3. Email sending is optional - form submissions will still work without it

## Testing Your Setup

### 1. Local Testing
```bash
# Start development server
npm run dev

# Test API endpoints
npm run test:api http://localhost:5000
```

### 2. Production Testing
```bash
# Test your deployed app
npm run test:api https://your-app.vercel.app
```

### 3. Manual Testing
1. Open browser developer tools
2. Go to Network tab
3. Try submitting a form
4. Check for any failed requests

## Environment Variables Checklist

### Required for Production:
- [ ] `SUPABASE_URL`
- [ ] `SUPABASE_SERVICE_ROLE_KEY`

### Optional:
- [ ] `RESEND_API_KEY` (for email notifications)
- [ ] `NODE_ENV=production`

## Getting Help

If you're still experiencing issues:

1. Check the browser console for JavaScript errors
2. Check Vercel function logs in your dashboard
3. Use the health endpoint to verify configuration: `/api/health`
4. Run the test script to identify specific failing endpoints

## Quick Fixes

### Reset Vercel Deployment
1. Go to Vercel Dashboard
2. Redeploy your latest commit
3. Check environment variables are properly set

### Clear Browser Cache
1. Hard refresh (Ctrl+F5 or Cmd+Shift+R)
2. Clear browser cache and cookies
3. Try in incognito/private mode
