@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;700&family=IBM+Plex+Mono:wght@500&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Marquee animation for the state flags banner */
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  animation: marquee 35s linear infinite;
  min-width: 100%;
  will-change: transform;
}

/* Custom card rotation classes */
.card-1 {
  transform: rotate(2deg);
  transition: transform 0.3s ease;
}

.card-2 {
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.card-3 {
  transform: rotate(-2deg);
  transition: transform 0.3s ease;
}

/* Clear rotation on hover */
.card-1:hover,
.card-2:hover,
.card-3:hover {
  transform: rotate(0deg) translateY(-4px);
}

:root {
  /* Custom AiLex brand colors */
  --primary: 195 90% 52%; /* #1EAEDB */
  --primary-foreground: 0 0% 100%; /* White text on primary */

  --navy: 210 59% 12%; /* #0C1C2D */
  --cool-gray: 220 20% 97%; /* #F5F7FA */
  --lime: 84 100% 68%; /* #B8FF5C */

  /* Base colors */
  --background: 0 0% 100%;
  --foreground: 210 59% 12%;
  --muted: 220 20% 97%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 0 0% 100%;
  --popover-foreground: 210 59% 12%;
  --card: 0 0% 100%;
  --card-foreground: 210 59% 12%;
  --border: 220 20% 97%;
  --input: 220 20% 97%;
  --secondary: 220 20% 97%;
  --secondary-foreground: 210 59% 12%;
  --accent: 84 100% 68%;
  --accent-foreground: 210 59% 12%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 210 59% 12%;
  --radius: 0.75rem;
}

.dark {
  --background: 210 59% 12%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 210 59% 12%;
  --popover-foreground: 0 0% 98%;
  --card: 210 59% 12%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 84 100% 68%;
  --accent-foreground: 210 59% 12%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Inter", sans-serif;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Inter", sans-serif;
    font-weight: 700;
  }

  .code {
    font-family: "IBM Plex Mono", monospace;
    font-weight: 500;
  }

  .container-content {
    @apply max-w-[1040px] mx-auto px-4 md:px-8;
  }

  .container-full {
    @apply max-w-[1280px] mx-auto px-4 md:px-8;
  }
}

@layer utilities {
  .transition-default {
    transition: all 250ms ease-out;
  }

  .grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary hover:bg-[#0C1C2D] hover:border hover:border-white text-white px-6 py-3 rounded-xl font-medium transition-default border border-transparent;
  }

  .btn-secondary {
    @apply border border-primary text-primary hover:bg-primary hover:bg-opacity-10 px-6 py-3 rounded-xl font-medium transition-default;
  }

  .btn-lime {
    @apply bg-[#B8FF5C] hover:bg-opacity-90 text-navy px-6 py-3 rounded-xl font-medium transition-default;
  }

  .card-hover {
    @apply transition-default hover:shadow-md;
  }

  .feature-card {
    @apply bg-navy bg-opacity-50 rounded-xl p-6 border border-gray-700 hover:border-primary transition-default relative overflow-hidden;
  }

  /* Mobile Navigation Improvements */
  .mobile-nav-item {
    @apply min-h-[44px] flex items-center;
  }

  .mobile-touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* Improved mobile menu animations */
  @media (max-width: 768px) {
    .container-full {
      @apply px-4;
    }

    /* Hero section mobile improvements */
    .hero-mobile-spacing {
      @apply gap-3;
    }

    /* Ensure proper spacing on very small screens */
    @media (max-width: 375px) {
      .container-full {
        @apply px-3;
      }

      /* Extra small screen adjustments */
      .hero-mobile-spacing {
        @apply gap-2;
      }
    }
  }
}

/* Animation keyframes */
@keyframes pulse {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.7;
  }
  50% {
    transform: translateY(10px);
    opacity: 1;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes scale {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* Animation classes */
.animate-pulse-slow {
  animation: pulse 3s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-scale {
  animation: scale 10s ease-in-out infinite;
}

.animate-blink {
  animation: blink 1s infinite;
}
