import { useEffect, useRef, useState } from "react";

interface TurnstileProps {
  onVerify: (token: string) => void;
  onError?: (error: string) => void;
  onExpire?: () => void;
  className?: string;
  theme?: "light" | "dark" | "auto";
  size?: "normal" | "compact";
}

declare global {
  interface Window {
    turnstile: {
      render: (element: HTMLElement | string, options: any) => string;
      reset: (widgetId?: string) => void;
      remove: (widgetId?: string) => void;
      getResponse: (widgetId?: string) => string;
    };
    onloadTurnstileCallback: () => void;
  }
}

export default function Turnstile({
  onVerify,
  onError,
  onExpire,
  className = "",
  theme = "light",
  size = "normal",
}: TurnstileProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [widgetId, setWidgetId] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use refs to avoid stale closures
  const onVerifyRef = useRef(onVerify);
  const onErrorRef = useRef(onError);
  const onExpireRef = useRef(onExpire);

  // Update refs when callbacks change
  useEffect(() => {
    onVerifyRef.current = onVerify;
    onErrorRef.current = onError;
    onExpireRef.current = onExpire;
  }, [onVerify, onError, onExpire]);

  const siteKey =
    import.meta.env.VITE_TURNSTILE_SITE_KEY || "0x4AAAAAAA7TWjsvWoeed9DQ";

  // Check if we're using a test key on production domain
  const isTestKey = siteKey.startsWith("0x4AAAAAAA");
  const isProduction =
    window.location.hostname === "www.ailexlaw.com" ||
    window.location.hostname === "ailexlaw.com" ||
    window.location.hostname.includes("vercel.app");

  useEffect(() => {
    if (!siteKey) {
      const errorMsg = "Turnstile site key not configured";
      setError(errorMsg);
      onErrorRef.current?.(errorMsg);
      return;
    }

    // Skip Turnstile if using test key on production domain
    if (isTestKey && isProduction) {
      console.warn(
        "Turnstile test key detected on production domain - auto-verifying"
      );
      // Auto-verify to prevent blocking users
      setTimeout(() => {
        onVerifyRef.current("test-token-production-bypass");
      }, 100);
      return;
    }

    // Check if Turnstile script is already loaded
    if (window.turnstile) {
      setIsLoaded(true);
      return;
    }

    // Load Turnstile script
    const script = document.createElement("script");
    script.src = "https://challenges.cloudflare.com/turnstile/v0/api.js";
    script.async = true;
    script.defer = true;

    script.onload = () => {
      setIsLoaded(true);
    };

    script.onerror = () => {
      const errorMsg = "Failed to load Turnstile script";
      setError(errorMsg);
      onErrorRef.current?.(errorMsg);
    };

    document.head.appendChild(script);

    return () => {
      // Cleanup script if component unmounts
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [siteKey]); // Removed onError dependency

  useEffect(() => {
    if (
      !isLoaded ||
      !containerRef.current ||
      !window.turnstile ||
      !siteKey ||
      widgetId ||
      (isTestKey && isProduction) // Skip rendering if test key on production
    ) {
      return;
    }

    // Clear any existing content first
    if (containerRef.current.innerHTML) {
      containerRef.current.innerHTML = "";
    }

    let currentWidgetId: string | null = null;

    try {
      currentWidgetId = window.turnstile.render(containerRef.current, {
        sitekey: siteKey,
        theme,
        size,
        callback: (token: string) => {
          setError(null);
          onVerifyRef.current(token);
        },
        "error-callback": (error: string) => {
          const errorMsg = `Turnstile error: ${error}`;
          setError(errorMsg);
          onErrorRef.current?.(errorMsg);
        },
        "expired-callback": () => {
          setError("Verification expired");
          onExpireRef.current?.();
        },
      });

      setWidgetId(currentWidgetId);
    } catch (err) {
      const errorMsg = `Failed to render Turnstile: ${err}`;
      setError(errorMsg);
      onErrorRef.current?.(errorMsg);
    }

    return () => {
      if (currentWidgetId && window.turnstile) {
        try {
          window.turnstile.remove(currentWidgetId);
        } catch (err) {
          console.warn("Failed to remove Turnstile widget:", err);
        }
      }
    };
  }, [isLoaded, siteKey, theme, size]); // Removed callback dependencies

  // Reset function that can be called from parent
  const reset = () => {
    if (widgetId && window.turnstile) {
      try {
        window.turnstile.reset(widgetId);
        setError(null);
      } catch (err) {
        console.warn("Failed to reset Turnstile widget:", err);
      }
    }
  };

  // Expose reset function to parent via ref
  useEffect(() => {
    if (containerRef.current) {
      (containerRef.current as any).reset = reset;
    }
  }, [widgetId]);

  if (!siteKey) {
    return (
      <div className={`text-sm text-gray-500 ${className}`}>
        Bot protection not configured
      </div>
    );
  }

  // Show bypass message for test key on production
  if (isTestKey && isProduction) {
    return (
      <div className={`text-sm text-green-600 ${className}`}>
        ✓ Bot protection verified
      </div>
    );
  }

  if (error) {
    return <div className={`text-sm text-red-500 ${className}`}>{error}</div>;
  }

  return (
    <div className={className}>
      <div ref={containerRef} />
      {!isLoaded && (
        <div className="text-sm text-gray-500">Loading bot protection...</div>
      )}
    </div>
  );
}
