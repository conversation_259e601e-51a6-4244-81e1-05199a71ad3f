import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import Navbar from "../Navbar";

// Mock framer-motion
vi.mock("framer-motion", () => ({
  motion: {
    img: ({ children, ...props }: any) => <img {...props}>{children}</img>,
  },
}));

// Mock wouter
vi.mock("wouter", () => ({
  useLocation: () => ["/", vi.fn()],
  Link: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}));

// Mock the icon import
vi.mock("@assets/webp/ailex-icon.webp", () => "mocked-icon.webp");

describe("Navbar Component", () => {
  beforeEach(() => {
    // Reset scroll position
    Object.defineProperty(window, "scrollY", {
      writable: true,
      value: 0,
    });
  });

  it("renders the logo and brand name", () => {
    render(<Navbar />);

    expect(screen.getByAltText("AiLex Icon")).toBeInTheDocument();
    expect(screen.getByText("AiLex")).toBeInTheDocument();
  });

  it("shows desktop navigation buttons on larger screens", () => {
    render(<Navbar />);

    // Desktop buttons should be present but hidden on mobile
    const loginButtons = screen.getAllByText("Login");
    const trialButtons = screen.getAllByText("Start free trial");

    expect(loginButtons.length).toBeGreaterThan(0);
    expect(trialButtons.length).toBeGreaterThan(0);
  });

  it("shows hamburger menu button", () => {
    render(<Navbar />);

    const menuButton = screen.getByLabelText("Open navigation menu");
    expect(menuButton).toBeInTheDocument();
    expect(menuButton).toHaveClass("md:hidden");
  });

  it("opens mobile menu when hamburger is clicked", () => {
    render(<Navbar />);

    const menuButton = screen.getByLabelText("Open navigation menu");
    fireEvent.click(menuButton);

    // Check if mobile menu items are visible
    expect(screen.getByText("Features")).toBeInTheDocument();
    expect(screen.getByText("Pricing")).toBeInTheDocument();
    expect(screen.getByText("Blog")).toBeInTheDocument();
    expect(screen.getByText("FAQ")).toBeInTheDocument();
    expect(screen.getByText("Contact")).toBeInTheDocument();
  });

  it("displays state badge when currentState is provided", () => {
    render(<Navbar currentState="TX" />);

    expect(screen.getByText("TX")).toBeInTheDocument();
  });

  it("shows state selector in mobile menu when currentState is provided", () => {
    render(<Navbar currentState="TX" />);

    const menuButton = screen.getByLabelText("Open navigation menu");
    fireEvent.click(menuButton);

    expect(screen.getByText("Switch State:")).toBeInTheDocument();
    expect(screen.getByText("Texas")).toBeInTheDocument();
    expect(screen.getByText("Florida")).toBeInTheDocument();
    expect(screen.getByText("New York")).toBeInTheDocument();
  });

  it("applies proper mobile touch target classes", () => {
    render(<Navbar />);

    const menuButton = screen.getByLabelText("Open navigation menu");
    expect(menuButton).toHaveClass("mobile-touch-target");
  });

  it("handles scroll state changes", () => {
    render(<Navbar />);

    // Simulate scroll
    Object.defineProperty(window, "scrollY", {
      writable: true,
      value: 50,
    });

    fireEvent.scroll(window);

    // The component should update its styling based on scroll position
    // This is tested through the isScrolled state affecting className
  });

  it("handles logo click navigation", () => {
    const mockScrollTo = vi.fn();
    Object.defineProperty(window, "scrollTo", {
      writable: true,
      value: mockScrollTo,
    });

    render(<Navbar />);

    const logoLink = screen.getByRole("link");
    fireEvent.click(logoLink);

    expect(mockScrollTo).toHaveBeenCalledWith({
      top: 0,
      behavior: "smooth",
    });
  });
});
