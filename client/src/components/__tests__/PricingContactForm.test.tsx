import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import Pricing from "../Pricing";

// Mock the prospect service
vi.mock("@/lib/prospectService", () => ({
  signupForContact: vi.fn().mockResolvedValue({}),
}));

// Mock the utils
vi.mock("@/lib/utils", () => ({
  storeUTMParameters: vi.fn(),
  isValidEmail: vi.fn((email: string) => email.includes("@")),
}));

// Mock the GDPR and Turnstile components
vi.mock("../GDPRConsent", () => ({
  default: ({ onConsentChange, onMarketingConsentChange }: any) => {
    return (
      <div data-testid="gdpr-consent">
        <input
          type="checkbox"
          data-testid="gdpr-checkbox"
          onChange={(e) => onConsentChange(e.target.checked)}
          defaultChecked
        />
        <input
          type="checkbox"
          data-testid="marketing-checkbox"
          onChange={(e) => onMarketingConsentChange?.(e.target.checked)}
          defaultChecked
        />
      </div>
    );
  },
}));

vi.mock("../Turnstile", () => ({
  default: ({ onVerify }: any) => {
    return (
      <div data-testid="turnstile">
        <button
          data-testid="turnstile-verify"
          onClick={() => onVerify("mock-token")}
        >
          Verify
        </button>
      </div>
    );
  },
}));

// Mock fetch
global.fetch = vi.fn().mockResolvedValue({
  ok: true,
  json: () => Promise.resolve({}),
});

describe("Pricing Contact Form Progressive Disclosure", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("initially shows only basic form fields without GDPR/Turnstile", () => {
    render(<Pricing />);

    // Basic form fields should be visible
    expect(screen.getByLabelText("First Name")).toBeInTheDocument();
    expect(screen.getByLabelText("Last Name")).toBeInTheDocument();
    expect(screen.getByLabelText("Email")).toBeInTheDocument();
    expect(screen.getByLabelText("Message")).toBeInTheDocument();

    // GDPR and Turnstile should NOT be visible initially
    expect(screen.queryByTestId("gdpr-consent-pricing")).not.toBeInTheDocument();
    expect(screen.queryByTestId("turnstile")).not.toBeInTheDocument();
  });

  it("shows GDPR and Turnstile components after user starts typing", async () => {
    render(<Pricing />);

    const firstNameInput = screen.getByLabelText("First Name");

    // Initially, GDPR and Turnstile should not be visible
    expect(screen.queryByText(/I agree to the processing/)).not.toBeInTheDocument();
    expect(screen.queryByTestId("turnstile")).not.toBeInTheDocument();

    // Start typing in the first name field
    fireEvent.change(firstNameInput, { target: { value: "John" } });

    // Wait for the progressive disclosure animation
    await waitFor(() => {
      expect(screen.getByText(/I agree to the processing/)).toBeInTheDocument();
    });

    // Turnstile should also be visible
    expect(screen.getByTestId("turnstile")).toBeInTheDocument();
  });

  it("maintains beautiful UI styling with blue theme", () => {
    render(<Pricing />);

    const firstNameInput = screen.getByLabelText("First Name");

    // Check that form has the blue gradient styling
    const form = firstNameInput.closest("form");
    expect(form).toBeInTheDocument();

    // Start typing to trigger progressive disclosure
    fireEvent.change(firstNameInput, { target: { value: "John" } });

    // Check that GDPR checkboxes have the correct styling classes
    waitFor(() => {
      const gdprCheckbox = screen.getByRole("checkbox", { name: /I agree to the processing/ });
      expect(gdprCheckbox).toHaveClass("text-[#B8FF5C]");
    });
  });

  it("validates GDPR and Turnstile only after user interaction", async () => {
    render(<Pricing />);

    const firstNameInput = screen.getByLabelText("First Name");
    const lastNameInput = screen.getByLabelText("Last Name");
    const emailInput = screen.getByLabelText("Email");
    const messageInput = screen.getByLabelText("Message");
    const submitButton = screen.getByRole("button", { name: /Send Message/ });

    // Fill out basic form fields
    fireEvent.change(firstNameInput, { target: { value: "John" } });
    fireEvent.change(lastNameInput, { target: { value: "Doe" } });
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(messageInput, { target: { value: "Test message" } });

    // Wait for progressive disclosure
    await waitFor(() => {
      expect(screen.getByText(/I agree to the processing/)).toBeInTheDocument();
    });

    // Submit button should be disabled until Turnstile is completed
    expect(submitButton).toBeDisabled();

    // Complete Turnstile verification
    const turnstileVerify = screen.getByTestId("turnstile-verify");
    fireEvent.click(turnstileVerify);

    // Now submit button should be enabled
    await waitFor(() => {
      expect(submitButton).not.toBeDisabled();
    });
  });
});
