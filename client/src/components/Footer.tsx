import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { CheckCircle, AlertCircle } from "lucide-react";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaLinkedinIn, FaGithub } from "react-icons/fa";
import ailexIcon from "@assets/webp/ailex-icon.webp";
import { signupForNewsletter } from "@/lib/prospectService";
import { storeUTMParameters, isValidEmail } from "@/lib/utils";
import GDPRConsent from "./GDPRConsent";
import Turnstile from "./Turnstile";

export default function Footer() {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState("");
  const [gdprConsent, setGdprConsent] = useState(true);
  const [showGdprForm, setShowGdprForm] = useState(false);
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);

  // Store UTM parameters on component mount
  useEffect(() => {
    storeUTMParameters();
  }, []);

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      setError("Please enter your email address");
      return;
    }

    if (!isValidEmail(email)) {
      setError("Please enter a valid email address");
      return;
    }

    // Show GDPR form if not already consented
    if (!gdprConsent) {
      setShowGdprForm(true);
      return;
    }

    if (!turnstileToken) {
      setError("Please complete the bot protection verification");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      // Use prospect API for newsletter signup
      await signupForNewsletter(email, {
        gdprConsent: gdprConsent,
        marketingConsent: true,
        turnstileToken: turnstileToken || undefined,
      });

      // Also send to legacy newsletter API for backward compatibility
      const legacyResponse = await fetch("/api/newsletter", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (!legacyResponse.ok) {
        console.warn(
          "Legacy newsletter API failed, but prospect signup succeeded"
        );
      }

      setIsSubmitted(true);
      setEmail("");
      setShowGdprForm(false);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "Failed to subscribe. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGdprSubmit = () => {
    if (gdprConsent) {
      handleNewsletterSubmit(new Event("submit") as any);
    }
  };

  return (
    <footer className="bg-navy text-white pt-16 pb-8">
      <div className="container-content">
        <div className="grid md:grid-cols-4 gap-8 mb-12">
          <div>
            <Link href="/" className="flex items-center mb-4">
              <img
                src={ailexIcon}
                alt="AiLex Icon"
                className="h-8 w-auto"
                loading="lazy"
                decoding="async"
              />
              <span className="ml-2 text-lg font-bold">AiLex</span>
            </Link>
            <p className="text-gray-400 text-sm">
              Built for small firms, powered by big tech.
            </p>

            <div className="flex space-x-4 mt-6">
              <a
                href="https://x.com/JustAskAiLex"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-primary transition-default"
              >
                <FaTwitter className="h-5 w-5" />
              </a>
              <a
                href="https://www.linkedin.com/company/*********"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-primary transition-default"
              >
                <FaLinkedinIn className="h-5 w-5" />
              </a>
            </div>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4">Product</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="#features"
                  onClick={(e) => {
                    e.preventDefault();
                    document.getElementById("features")?.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                  }}
                  className="text-gray-400 hover:text-white transition-default"
                >
                  Features
                </a>
              </li>
              <li>
                <a
                  href="#pricing"
                  onClick={(e) => {
                    e.preventDefault();
                    document.getElementById("pricing")?.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                  }}
                  className="text-gray-400 hover:text-white transition-default"
                >
                  Pricing
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4">Resources</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/blog"
                  className="text-gray-400 hover:text-white transition-default"
                >
                  Blog
                </Link>
              </li>
              <li>
                <a
                  href="#"
                  className="text-gray-400 hover:text-white transition-default"
                >
                  FAQ
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4">Get Weekly AI Tips</h3>
            <p className="text-gray-400 text-sm mb-4">
              Legal tech tips to help your practice grow.
            </p>

            {isSubmitted ? (
              <div className="flex items-center text-[#B8FF5C] text-sm">
                <CheckCircle className="w-4 h-4 mr-2" />
                Successfully subscribed! Check your email for confirmation.
              </div>
            ) : (
              <>
                {error && (
                  <div className="mb-2 flex items-center text-red-400 text-sm">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    {error}
                  </div>
                )}

                {!showGdprForm ? (
                  <form onSubmit={handleNewsletterSubmit} className="space-y-3">
                    <div className="flex">
                      <input
                        type="email"
                        placeholder="Email address"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        disabled={isSubmitting}
                        className="px-4 py-2 rounded-l-lg w-full focus:outline-none focus:ring-2 focus:ring-[#1EAEDB] text-gray-900 bg-white disabled:opacity-50"
                      />
                      <button
                        type="submit"
                        disabled={isSubmitting || !email}
                        className="bg-[#1EAEDB] hover:bg-[#1EAEDB]/90 text-white px-4 rounded-r-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? "..." : "Subscribe"}
                      </button>
                    </div>
                  </form>
                ) : (
                  <div className="space-y-3">
                    <div className="flex">
                      <input
                        type="email"
                        placeholder="Email address"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        disabled={true}
                        className="px-4 py-2 rounded-l-lg w-full focus:outline-none focus:ring-2 focus:ring-[#1EAEDB] text-gray-900 bg-gray-100"
                      />
                      <button
                        type="button"
                        disabled={true}
                        className="bg-gray-400 text-white px-4 rounded-r-lg cursor-not-allowed"
                      >
                        Subscribe
                      </button>
                    </div>

                    <GDPRConsent
                      onConsentChange={setGdprConsent}
                      className="text-xs"
                    />

                    <Turnstile
                      onVerify={setTurnstileToken}
                      onError={(error) =>
                        setError(`Bot protection error: ${error}`)
                      }
                      onExpire={() => setTurnstileToken(null)}
                      className="flex justify-center"
                      size="compact"
                    />

                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={handleGdprSubmit}
                        disabled={
                          !gdprConsent || !turnstileToken || isSubmitting
                        }
                        className="bg-[#1EAEDB] hover:bg-[#1EAEDB]/90 text-white px-3 py-1 rounded text-xs transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? "..." : "Confirm"}
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setShowGdprForm(false);
                          setGdprConsent(true);
                          setTurnstileToken(null);
                        }}
                        className="text-gray-400 hover:text-white px-3 py-1 rounded text-xs transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-500 text-sm mb-4 md:mb-0 flex flex-wrap items-center gap-4">
            <span>
              © {new Date().getFullYear()} AiLex Law. All rights reserved.
            </span>
            <Link
              href="/privacy-policy"
              className="hover:text-gray-300 transition-default"
            >
              Privacy Policy
            </Link>
            <a href="#" className="hover:text-gray-300 transition-default">
              Terms of Service
            </a>
          </div>

          <div className="flex flex-wrap text-sm text-gray-500">
            <motion.span
              initial={{ opacity: 0.5 }}
              whileHover={{
                opacity: 1,
                color: "#B8FF5C",
                transition: { duration: 0.3 },
              }}
            >
              Made with ❤️ with solo attorneys & small firms in mind
            </motion.span>
          </div>
        </div>
      </div>
    </footer>
  );
}
