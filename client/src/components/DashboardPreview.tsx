import { motion } from "framer-motion";
import { type State } from "@/types";
import { useIsMobile } from "@/hooks/use-mobile";

type DashboardPreviewProps = {
  state?: State;
};

export default function DashboardPreview({ state }: DashboardPreviewProps) {
  const isMobile = useIsMobile();

  return (
    <motion.div
      className="relative flex justify-center items-center perspective-[2000px]"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Background Gradient for 3D effect */}
      <div className="absolute inset-0 bg-gradient-to-b from-white via-blue-50 to-blue-100 opacity-60 blur-2xl rounded-full"></div>

      {/* Card wrapper with perspective */}
      <motion.div
        className={`relative ${isMobile ? "" : "animate-float-slow"}`}
        whileHover={{
          scale: isMobile ? 1.02 : 0.97,
          y: isMobile ? -3 : -2,
          transition: { duration: 0.3 },
        }}
      >
        {/* Glow effect behind the card */}
        <div className="absolute inset-0 bg-blue-400/20 rounded-3xl blur-xl transform -translate-y-2"></div>

        {/* Main card container */}
        <div className="bg-white/70 backdrop-blur-md rounded-3xl shadow-[0px_30px_60px_rgba(0,0,0,0.2)] p-6 w-full max-w-sm relative">
          {/* Decorative elements */}
          <div className="absolute -top-2 -right-2 w-16 h-16 bg-blue-50 rounded-full blur-lg opacity-70"></div>
          <div className="absolute bottom-10 -left-4 w-10 h-10 bg-[#B8FF5C]/30 rounded-full blur-md"></div>

          {/* Header section */}
          <div className="flex items-center justify-between mb-6 relative z-10">
            <div className="flex items-center">
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-full w-10 h-10 flex items-center justify-center text-white font-bold shadow-md">
                AI
              </div>
              <div className="ml-3">
                <p className="text-sm font-semibold text-gray-800">
                  AiLex Dashboard
                </p>
                <p className="text-xs text-gray-500">
                  Firm: Smith & Associates
                </p>
              </div>
            </div>
            <div className="px-3 py-1 bg-[#B8FF5C] bg-opacity-90 text-navy text-xs font-semibold rounded-full shadow-sm flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-1.5 animate-pulse"></span>
              Ready
            </div>
          </div>

          {/* Active matters card */}
          <div className="bg-white rounded-xl shadow-sm p-4 mb-5 border border-gray-100">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
                  Active Matters
                </p>
                <p className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
                  14
                </p>
              </div>
              <div className="h-12 w-12 rounded-lg bg-blue-50 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-blue-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
            </div>
            <div className="mt-2 flex items-center">
              <span className="text-xs text-green-500 font-medium flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3 w-3 mr-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z"
                    clipRule="evenodd"
                  />
                </svg>
                +2 this week
              </span>
              <div className="ml-auto flex space-x-1">
                <div className="w-6 h-2 rounded-full bg-blue-400"></div>
                <div className="w-3 h-2 rounded-full bg-gray-200"></div>
                <div className="w-2 h-2 rounded-full bg-gray-200"></div>
              </div>
            </div>
          </div>

          {/* Activity and schedule section */}
          <div className="grid grid-cols-1 gap-4 mb-5">
            {/* Activity section */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <p className="text-xs uppercase font-semibold text-gray-500 tracking-wider">
                  Recent Activity
                </p>
                <span className="text-xs text-blue-500 font-medium hover:text-blue-600 cursor-pointer">
                  See all
                </span>
              </div>

              <div className="space-y-2.5">
                <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-50 hover:border-blue-100 transition-all duration-200">
                  <div className="flex items-start">
                    <div className="h-6 w-6 rounded bg-blue-50 flex items-center justify-center mr-2 mt-0.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3.5 w-3.5 text-blue-500"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-800">
                        Document Analysis
                      </p>
                      <p className="text-xs text-gray-400">
                        Bailey v. State.pdf
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-50 hover:border-blue-100 transition-all duration-200">
                  <div className="flex items-start">
                    <div className="h-6 w-6 rounded bg-blue-50 flex items-center justify-center mr-2 mt-0.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3.5 w-3.5 text-blue-500"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-800">
                        Client Intake
                      </p>
                      <p className="text-xs text-gray-400">Rodriguez Estate</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Upcoming section */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <p className="text-xs uppercase font-semibold text-gray-500 tracking-wider">
                  Upcoming
                </p>
                <span className="text-xs text-blue-500 font-medium hover:text-blue-600 cursor-pointer">
                  Schedule
                </span>
              </div>

              <div className="space-y-2.5">
                <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-50 hover:border-blue-100 transition-all duration-200">
                  <div className="flex items-start">
                    <div className="h-6 w-6 rounded bg-orange-50 flex items-center justify-center mr-2 mt-0.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3.5 w-3.5 text-orange-500"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-800">
                        Filing Deadline
                      </p>
                      <p className="text-xs text-gray-400">
                        May 15 - Johnson Case
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-50 hover:border-blue-100 transition-all duration-200">
                  <div className="flex items-start">
                    <div className="h-6 w-6 rounded bg-purple-50 flex items-center justify-center mr-2 mt-0.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3.5 w-3.5 text-purple-500"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-800">
                        Court Hearing
                      </p>
                      <p className="text-xs text-gray-400">
                        May 20 - Rodriguez
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom action button */}
          <div className="flex justify-end">
            <button className="bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs font-medium px-4 py-2 rounded-lg hover:shadow-lg transform hover:translate-y-0.5 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-opacity-50">
              Open Dashboard
            </button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
