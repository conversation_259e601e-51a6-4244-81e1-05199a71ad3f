import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Info } from "lucide-react";

interface GDPRConsentProps {
  onConsentChange: (consent: boolean) => void;
  required?: boolean;
  className?: string;
  showMarketingConsent?: boolean;
  onMarketingConsentChange?: (consent: boolean) => void;
}

export default function GDPRConsent({
  onConsentChange,
  required = true,
  className = "",
  showMarketingConsent = false,
  onMarketingConsentChange,
}: GDPRConsentProps) {
  const [gdprConsent, setGdprConsent] = useState(true);
  const [marketingConsent, setMarketingConsent] = useState(true);
  const [showDetails, setShowDetails] = useState(false);

  // Initialize parent state with default values
  useEffect(() => {
    onConsentChange(true);
    if (showMarketingConsent) {
      onMarketingConsentChange?.(true);
    }
  }, []); // Run only on mount

  const handleGdprChange = (checked: boolean) => {
    setGdprConsent(checked);
    onConsentChange(checked);
  };

  const handleMarketingChange = (checked: boolean) => {
    setMarketingConsent(checked);
    onMarketingConsentChange?.(checked);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* GDPR Consent (Required) */}
      <div className="flex items-start space-x-3">
        <input
          type="checkbox"
          id="gdpr-consent"
          checked={gdprConsent}
          onChange={(e) => handleGdprChange(e.target.checked)}
          className="mt-1 h-4 w-4 text-[#1EAEDB] focus:ring-[#1EAEDB] border-gray-300 rounded"
          required={required}
        />
        <div className="flex-1">
          <label
            htmlFor="gdpr-consent"
            className="text-sm text-gray-700 cursor-pointer"
          >
            I agree to the processing of my personal data in accordance with the{" "}
            <a
              href="/privacy-policy"
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#1EAEDB] hover:underline"
            >
              Privacy Policy
            </a>
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        </div>
      </div>

      {/* Marketing Consent (Optional) */}
      {showMarketingConsent && (
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="marketing-consent"
            checked={marketingConsent}
            onChange={(e) => handleMarketingChange(e.target.checked)}
            className="mt-1 h-4 w-4 text-[#1EAEDB] focus:ring-[#1EAEDB] border-gray-300 rounded"
          />
          <div className="flex-1">
            <label
              htmlFor="marketing-consent"
              className="text-sm text-gray-700 cursor-pointer"
            >
              I would like to receive marketing communications, newsletters, and
              updates about AiLex products and services.
            </label>
          </div>
        </div>
      )}

      {/* Information Toggle */}
      <div className="flex items-center space-x-2">
        <button
          type="button"
          onClick={() => setShowDetails(!showDetails)}
          className="flex items-center text-xs text-gray-500 hover:text-gray-700 transition-colors"
        >
          <Info className="w-3 h-3 mr-1" />
          {showDetails ? "Hide" : "Show"} data processing details
        </button>
      </div>

      {/* Detailed Information */}
      <motion.div
        initial={false}
        animate={{
          height: showDetails ? "auto" : 0,
          opacity: showDetails ? 1 : 0,
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        <div className="bg-gray-50 p-3 rounded-lg text-xs text-gray-600 space-y-2">
          <p>
            <strong>Data Controller:</strong> AiLex Legal Assistant
          </p>
          <p>
            <strong>Purpose:</strong> To process your inquiry, provide customer
            support, and send relevant information about our services.
          </p>
          <p>
            <strong>Legal Basis:</strong> Legitimate interest for business
            communication and consent for marketing communications.
          </p>
          <p>
            <strong>Data Retention:</strong> We will retain your data for as
            long as necessary to fulfill the purposes outlined above, or as
            required by law.
          </p>
          <p>
            <strong>Your Rights:</strong> You have the right to access, rectify,
            erase, restrict processing, object to processing, and data
            portability. You can withdraw consent at any time.
          </p>
          <p>
            <strong>Contact:</strong> For any questions about data processing,
            contact us at{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-[#1EAEDB] hover:underline"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </motion.div>
    </div>
  );
}
