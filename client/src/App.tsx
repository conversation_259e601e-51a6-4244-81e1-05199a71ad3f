import { Switch, Route } from "wouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { Suspense, lazy } from "react";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { PageSpinner } from "@/components/ui/spinner";

// Lazy load all pages for code splitting
const Home = lazy(
  () => import(/* webpackChunkName: "page-home" */ "@/pages/Home")
);
const StatePage = lazy(
  () => import(/* webpackChunkName: "page-state" */ "@/pages/StatePage")
);
const Blog = lazy(
  () => import(/* webpackChunkName: "page-blog" */ "@/pages/Blog")
);
const BlogPost = lazy(
  () => import(/* webpackChunkName: "page-blog-post" */ "@/pages/BlogPost")
);
const Login = lazy(
  () => import(/* webpackChunkName: "page-login" */ "@/pages/Login")
);
const SecurityWhitepaper = lazy(
  () =>
    import(/* webpackChunkName: "page-security" */ "@/pages/SecurityWhitepaper")
);
const Demo = lazy(
  () => import(/* webpackChunkName: "page-demo" */ "@/pages/Demo")
);
const VerifyEmail = lazy(
  () =>
    import(/* webpackChunkName: "page-verify-email" */ "@/pages/VerifyEmail")
);
const PrivacyPolicy = lazy(
  () =>
    import(
      /* webpackChunkName: "page-privacy-policy" */ "@/pages/PrivacyPolicy"
    )
);
const NotFound = lazy(
  () => import(/* webpackChunkName: "page-404" */ "@/pages/not-found")
);

function Router() {
  return (
    <Suspense fallback={<PageSpinner />}>
      <Switch>
        <Route path="/" component={Home} />
        <Route path="/tx" component={() => <StatePage state="TX" />} />
        <Route path="/fl" component={() => <StatePage state="FL" />} />
        <Route path="/ny" component={() => <StatePage state="NY" />} />
        <Route path="/blog" component={Blog} />
        <Route path="/blog/:slug" component={BlogPost} />
        <Route path="/login" component={Login} />
        {/* Fixed: Added /demo route for Watch Demo button */}
        <Route path="/demo" component={Demo} />
        <Route path="/verify-email" component={VerifyEmail} />
        <Route path="/verify-email/:token" component={VerifyEmail} />
        <Route path="/security-whitepaper" component={SecurityWhitepaper} />
        <Route path="/privacy-policy" component={PrivacyPolicy} />
        {/* Fallback to 404 */}
        <Route component={NotFound} />
      </Switch>
    </Suspense>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
