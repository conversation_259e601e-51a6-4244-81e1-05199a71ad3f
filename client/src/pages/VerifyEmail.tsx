import { useEffect, useState } from "react";
import { useLocation, useRoute } from "wouter";
import { motion } from "framer-motion";
import { CheckCircle, AlertCircle, Mail, ArrowLeft } from "lucide-react";
import { ProspectService } from "@/lib/prospectService";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export default function VerifyEmail() {
  const [, setLocation] = useLocation();
  const [, params] = useRoute("/verify-email/:token?");
  const [verificationState, setVerificationState] = useState<
    "loading" | "success" | "error" | "invalid"
  >("loading");
  const [message, setMessage] = useState("");

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const token = params?.token || urlParams.get("token");

    if (!token) {
      setVerificationState("invalid");
      setMessage(
        "No verification token provided. Please check your email for the correct verification link."
      );
      return;
    }

    verifyEmailToken(token);
  }, [params]);

  const verifyEmailToken = async (token: string) => {
    try {
      setVerificationState("loading");
      const response = await ProspectService.verifyEmail(token);

      if (response.success) {
        setVerificationState("success");
        setMessage(
          response.message || "Your email has been successfully verified!"
        );
      } else {
        setVerificationState("error");
        setMessage(
          response.message || "Email verification failed. Please try again."
        );
      }
    } catch (error) {
      setVerificationState("error");
      setMessage(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred during verification."
      );
    }
  };

  const getIcon = () => {
    switch (verificationState) {
      case "success":
        return <CheckCircle className="w-16 h-16 text-green-500" />;
      case "error":
      case "invalid":
        return <AlertCircle className="w-16 h-16 text-red-500" />;
      default:
        return <Mail className="w-16 h-16 text-[#1EAEDB] animate-pulse" />;
    }
  };

  const getTitle = () => {
    switch (verificationState) {
      case "success":
        return "Email Verified Successfully!";
      case "error":
        return "Verification Failed";
      case "invalid":
        return "Invalid Verification Link";
      default:
        return "Verifying Your Email...";
    }
  };

  const getActionButton = () => {
    if (verificationState === "success") {
      return (
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setLocation("/")}
          className="bg-[#1EAEDB] hover:bg-[#1EAEDB]/90 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
        >
          Continue to AiLex
        </motion.button>
      );
    }

    return (
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setLocation("/")}
        className="flex items-center text-[#1EAEDB] hover:text-[#1EAEDB]/80 font-semibold transition-colors"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Home
      </motion.button>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navbar />

      <main className="flex-1 flex items-center justify-center px-6 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-md w-full bg-white rounded-2xl shadow-lg p-8 text-center"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="flex justify-center mb-6"
          >
            {getIcon()}
          </motion.div>

          <motion.h1
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="text-2xl font-bold text-gray-900 mb-4"
          >
            {getTitle()}
          </motion.h1>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="text-gray-600 mb-8 leading-relaxed"
          >
            {message}
          </motion.p>

          {verificationState === "success" && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6"
            >
              <p className="text-green-800 text-sm">
                You're now subscribed to our newsletter and will receive updates
                about AiLex features and legal tech insights.
              </p>
            </motion.div>
          )}

          {(verificationState === "error" ||
            verificationState === "invalid") && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
            >
              <p className="text-red-800 text-sm">
                If you continue to experience issues, please contact our support
                team at{" "}
                <a href="mailto:<EMAIL>" className="underline">
                  <EMAIL>
                </a>
              </p>
            </motion.div>
          )}

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
          >
            {getActionButton()}
          </motion.div>
        </motion.div>
      </main>

      <Footer />
    </div>
  );
}
