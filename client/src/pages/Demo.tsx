// Demo page - Fixed 404 button issues - v1.0.1
import { useEffect } from "react";
import { <PERSON> } from "wouter";
import { motion } from "framer-motion";
import { ArrowRight, Play, Clock, Users, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { updateMetaTags, pageSEO } from "@/lib/seo";

export default function Demo() {
  // Set SEO meta tags
  useEffect(() => {
    updateMetaTags({
      title: "AiLex Demo - See AI Legal Assistant in Action",
      description:
        "Watch a 90-second demo of <PERSON><PERSON><PERSON>, the AI-powered legal assistant that handles research, drafts documents, and manages client intake for solo practitioners.",
      keywords:
        "AiLex demo, legal AI demonstration, legal assistant software, AI legal research, legal document drafting",
      ogTitle: "AiLex Demo - AI Legal Assistant in Action",
      ogDescription:
        "See how AiLex transforms legal practice with AI-powered research, document drafting, and client management.",
      canonical: "https://ailex.law/demo",
    });
  }, []);

  const demoFeatures = [
    {
      icon: <FileText className="w-6 h-6" />,
      title: "Legal Research",
      description: "Watch AiLex find relevant cases and statutes in seconds",
      duration: "30 sec",
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Document Drafting",
      description: "See how AiLex drafts motions and legal memos",
      duration: "30 sec",
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Client Intake",
      description: "Experience automated client intake and scheduling",
      duration: "30 sec",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F5F7FA] via-white to-[#1EAEDB]/5">
      <Navbar />

      <div className="pt-24 pb-20">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            {/* Back to Home Link */}
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-8"
            >
              <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
              Back to Home
            </Link>

            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              See AiLex in Action
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed max-w-2xl mx-auto mb-8">
              Watch how AiLex transforms legal practice with AI-powered
              research, document drafting, and client management in just 90
              seconds.
            </p>
          </motion.div>

          {/* Main Demo Video Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-2xl shadow-xl overflow-hidden mb-12"
          >
            <div className="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center relative">
              {/* Video Placeholder */}
              <div className="text-center">
                <motion.div
                  className="w-20 h-20 bg-[#1EAEDB] rounded-full flex items-center justify-center mb-4 mx-auto cursor-pointer"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Play className="w-8 h-8 text-white ml-1" />
                </motion.div>
                <h3 className="text-white text-xl font-semibold mb-2">
                  AiLex Complete Demo
                </h3>
                <p className="text-gray-300">90 seconds • Full walkthrough</p>
              </div>

              {/* Coming Soon Badge */}
              <div className="absolute top-4 right-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                Demo Video Coming Soon
              </div>
            </div>
          </motion.div>

          {/* Feature Breakdown */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mb-12"
          >
            <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
              What You'll See in the Demo
            </h2>
            <div className="grid md:grid-cols-3 gap-6">
              {demoFeatures.map((feature, index) => (
                <div
                  key={index}
                  className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-[#1EAEDB]/10 rounded-lg flex items-center justify-center text-[#1EAEDB] mr-4">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {feature.title}
                      </h3>
                      <span className="text-sm text-gray-500">
                        {feature.duration}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </motion.div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="text-center bg-gradient-to-r from-[#1EAEDB]/5 to-[#1EAEDB]/10 rounded-2xl p-8"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Transform Your Practice?
            </h2>
            <p className="text-gray-600 mb-6 max-w-lg mx-auto">
              Join the waitlist for early access to AiLex and be among the first
              to experience AI-powered legal assistance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/login">
                <Button className="bg-[#1EAEDB] hover:bg-[#189eca] text-white px-8 py-3">
                  Request Early Access
                </Button>
              </Link>
              <Link href="/#pricing">
                <Button
                  variant="outline"
                  className="border-[#1EAEDB] text-[#1EAEDB] hover:bg-[#1EAEDB] hover:text-white px-8 py-3"
                >
                  View Pricing
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
