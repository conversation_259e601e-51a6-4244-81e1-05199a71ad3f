// State type for locations
export type State = "TX" | "FL" | "NY";

// Testimonial type
export interface Testimonial {
  id: number;
  name: string;
  firm: string;
  state: State;
  quote: string;
  rating?: number;
  hasVideo?: boolean;
}

// Pricing plan type
export interface PricingPlan {
  name: string;
  subtitle: string;
  price: number;
  features: string[];
  disabledFeatures: string[];
  popular: boolean;
  cta: string;
}

// FAQ type
export interface FAQ {
  question: string;
  answer: string;
}

// Security feature type
export interface SecurityFeature {
  title: string;
  description: string;
  icon: React.ReactNode;
}

// Feature card type
export interface FeatureCard {
  id: number;
  code: string;
  title: string;
  description: string;
  demoTitle: string;
  demoContent: React.ReactNode;
}

// Problem card type
export interface ProblemCard {
  emoji: string;
  title: string;
  description: string;
}

// Logo type
export interface Logo {
  name: string;
  altText: string;
}

// Navigation item type
export interface NavItem {
  label: string;
  href: string;
  external?: boolean;
}

// Form submission type
export interface ContactSubmission {
  email: string;
  name?: string;
  message?: string;
}

// Prospect signup types
export interface ProspectSignupRequest {
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  signupSource:
    | "website"
    | "landing_page"
    | "referral"
    | "social_media"
    | "advertisement"
    | "other";
  signupPage?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmContent?: string;
  utmTerm?: string;
  practiceAreaInterest?: (
    | "personal_injury"
    | "criminal_defense"
    | "family_law"
  )[];
  caseUrgency?:
    | "immediate"
    | "within_month"
    | "within_quarter"
    | "planning_ahead";
  estimatedCaseValue?:
    | "under_10k"
    | "10k_50k"
    | "50k_100k"
    | "over_100k"
    | "unknown";
  newsletterSubscribed?: boolean;
  marketingConsent?: boolean;
  communicationPreferences?: {
    email: boolean;
    sms: boolean;
    phone: boolean;
  };
  gdprConsent: boolean;
  turnstileToken?: string;
}

export interface ProspectSignupResponse {
  success: boolean;
  message: string;
  prospectId?: string;
  emailVerified?: boolean;
  error?: string;
}

export interface EmailVerificationRequest {
  token: string;
}

export interface UTMParameters {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_content?: string;
  utm_term?: string;
}
